defmodule Drops.Relation.Plugins.Queryable do
  alias Drops.Relation.Generator
  alias Drops.Relation.Plugins.Queryable.Operations

  use Drops.Relation.Plugin do
    defstruct([:repo, :schema, :queryable, operations: [], opts: []])
  end

  def on(:before_compile, relation, %{opts: opts}) do
    ecto_schema_module = ecto_schema_module(relation)

    ecto_funcs =
      quote do
        defdelegate __schema__(key), to: unquote(ecto_schema_module)
        defdelegate __schema__(key, value), to: unquote(ecto_schema_module)

        def __schema_module__, do: unquote(ecto_schema_module)
      end

    quote do
      unquote(ecto_funcs)

      @spec repo() :: module()
      def repo, do: unquote(opts[:repo])

      def new(), do: new([])

      def new(), do: new([])

      def new(opts) do
        new(__schema_module__(), opts)
      end

      def new(queryable, opts) do
        Kernel.struct(__MODULE__, %{
          queryable: queryable,
          schema: schema(),
          repo: repo(),
          operations: [],
          opts: opts
        })
      end
    end
  end

  def on(:after_compile, relation, _) do
    schema = context(relation, :schema)
    ecto_schema = Generator.generate_module_content(relation.schema(), schema.block || [])

    Module.create(
      relation.__schema_module__(),
      ecto_schema,
      Macro.Env.location(__ENV__)
    )

    quote location: :keep do
      defimpl Ecto.Queryable, for: unquote(relation) do
        @compilers [
          restrict: Operations.Restrict.FieldsCompiler,
          order: Operations.Order.FieldsCompiler,
          preload: Operations.Preload.AssociationCompiler
        ]

        def to_query(%{operations: []}) do
          Ecto.Queryable.to_query(relation.queryable)
        end

        def to_query(%{operations: operations, opts: opts} = relation) do
          Enum.reduce(operations, Ecto.Queryable.to_query(queryable), fn name, query ->
            @compilers[name].visit(relation, %{query: query, opts: opts})
          end)
        end
      end
    end
  end

  def ecto_schema_module(relation) do
    namespace = config(relation, :ecto_schema_namespace)

    module =
      case context(relation, :schema).opts[:struct] do
        nil ->
          config(relation, :ecto_schema_module)

        value ->
          value
      end

    Module.concat(namespace ++ [module])
  end
end
